# cgame_avatar_factory\hair_studio\maya_api\utils.py

"""Maya API Utilities.

This module provides common utility functions for Maya API operations,
including unified Maya import and mock management.
"""

# Import built-in modules
import logging
import re


class MayaMockCmds(object):
    """
    Unified mock class for maya.cmds when Maya is not available.

    This provides consistent mock behavior across all modules and
    centralizes the mock implementation for easier maintenance.
    """

    def __init__(self, logger_name=None):
        """
        Initialize the mock with optional logger name.

        Args:
            logger_name (str, optional): Logger name for mock messages
        """
        self.logger = logging.getLogger(logger_name or __name__)
        self._call_count = 0

    def __getattr__(self, item):
        """
        Mock any Maya command call.

        Args:
            item (str): Maya command name

        Returns:
            function: Mock function that logs the call
        """

        def wrapper(*args, **kwargs):
            self._call_count += 1
            self.logger.warning(
                "Maya command called outside of Maya: {}.{} {} {}".format(
                    item,
                    args,
                    kwargs,
                    f"(call #{self._call_count})",
                ),
            )
            return f"mock_{item}_{self._call_count}"

        return wrapper

    def get_call_count(self):
        """Get the total number of mock calls made."""
        return self._call_count

    def reset_call_count(self):
        """Reset the call counter."""
        self._call_count = 0


# Global Maya availability check and import
_maya_cmds = None
_maya_available = None


def get_maya_cmds(logger_name=None):
    """
    Get maya.cmds with unified mock fallback.

    This function provides a centralized way to import maya.cmds
    with consistent mock behavior when Maya is not available.

    Args:
        logger_name (str, optional): Logger name for mock messages

    Returns:
        maya.cmds or MayaMockCmds: Real or mock Maya commands
    """
    global _maya_cmds, _maya_available

    if _maya_cmds is None:
        try:
            # Import third-party modules
            import maya.cmds as cmds

            _maya_cmds = cmds
            _maya_available = True
        except ImportError:
            _maya_cmds = MayaMockCmds(logger_name)
            _maya_available = False

    return _maya_cmds


def is_maya_available():
    """
    Check if Maya is available.

    Returns:
        bool: True if Maya is available, False otherwise
    """
    global _maya_available

    if _maya_available is None:
        # Trigger the import check
        get_maya_cmds()

    return _maya_available


MAYA_VERSION_PATTERN = re.compile(r"20\d{2}")


def get_maya_version():
    """
    Get Maya version number.

    Returns:
        int or None: Maya version as year (e.g., 2022, 2023) or None if not available
    """
    logger = logging.getLogger(__name__)

    if not is_maya_available():
        logger.debug("Maya not available, cannot determine version")
        return None

    try:
        cmds = get_maya_cmds(__name__)
        if hasattr(cmds, "about"):
            version_string = cmds.about(version=True)
            # Extract year from version string (e.g., "2022", "2023")
            match = MAYA_VERSION_PATTERN.search(version_string)
            if match:
                version = int(match.group())
                logger.debug(f"Detected Maya version: {version}")
                return version
            else:
                logger.warning(f"Could not parse Maya version from: {version_string}")
        else:
            logger.warning("cmds.about not available")
        return None
    except Exception as e:
        logger.error(f"Error determining Maya version: {e}")
        return None

################################################################# import asset utils##############################################################
def get_scene_up_axis():
    pass


def check_file_yz_up():
    pass

def check_fbx_zy_up():
    pass

def check_obj_yz_up():
    pass
















################################################################# import asset utils##############################################################